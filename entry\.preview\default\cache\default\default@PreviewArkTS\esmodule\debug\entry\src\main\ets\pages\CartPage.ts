if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface CartPage_Params {
    cartItems?: CartItem[];
    cartViewModel?: CartViewModel;
}
import { CartViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/CartViewModel&";
import type { CartItem } from '../model/CartItem';
import { Constants } from "@normalized:N&&&entry/src/main/ets/common/Constants&";
import router from "@ohos:router";
class CartPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cartItems = this.createStorageLink(Constants.SHOPPING_CART, [], "cartItems");
        this.cartViewModel = new CartViewModel();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: CartPage_Params) {
        if (params.cartViewModel !== undefined) {
            this.cartViewModel = params.cartViewModel;
        }
    }
    updateStateVars(params: CartPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cartItems.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cartItems.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cartItems: ObservedPropertyAbstractPU<CartItem[]>;
    get cartItems() {
        return this.__cartItems.get();
    }
    set cartItems(newValue: CartItem[]) {
        this.__cartItems.set(newValue);
    }
    private cartViewModel: CartViewModel;
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CartPage.ets(16:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f5f5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CartPage.ets(18:7)", "entry");
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.backgroundColor('#f8f8f8');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/CartPage.ets(19:9)", "entry");
            Button.onClick(() => {
                router.back();
            });
            Button.margin({ left: 16 });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('购物车');
            Text.debugLine("entry/src/main/ets/pages/CartPage.ets(25:9)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`共${this.cartViewModel.getTotalQuantity()}件商品`);
            Text.debugLine("entry/src/main/ets/pages/CartPage.ets(31:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666');
            Text.margin({ right: 16 });
        }, Text);
        Text.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.cartItems.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 空购物车状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/CartPage.ets(42:9)", "entry");
                        // 空购物车状态
                        Column.width('100%');
                        // 空购物车状态
                        Column.height('60%');
                        // 空购物车状态
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('购物车是空的');
                        Text.debugLine("entry/src/main/ets/pages/CartPage.ets(43:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('去购物');
                        Button.debugLine("entry/src/main/ets/pages/CartPage.ets(47:11)", "entry");
                        Button.margin({ top: 20 });
                        Button.onClick(() => {
                            router.back();
                        });
                    }, Button);
                    Button.pop();
                    // 空购物车状态
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 购物车列表
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/CartPage.ets(58:9)", "entry");
                        // 购物车列表
                        List.layoutWeight(1);
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const item = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.debugLine("entry/src/main/ets/pages/CartPage.ets(60:13)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Row.create();
                                        Row.debugLine("entry/src/main/ets/pages/CartPage.ets(61:15)", "entry");
                                        Row.width('100%');
                                        Row.padding(16);
                                    }, Row);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        // 商品图片
                                        Stack.create({ alignContent: Alignment.Center });
                                        Stack.debugLine("entry/src/main/ets/pages/CartPage.ets(63:17)", "entry");
                                        // 商品图片
                                        Stack.width(60);
                                        // 商品图片
                                        Stack.height(60);
                                        // 商品图片
                                        Stack.backgroundColor('#F9F9F9');
                                        // 商品图片
                                        Stack.borderRadius(8);
                                        // 商品图片
                                        Stack.margin({ right: 12 });
                                    }, Stack);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Image.create(item.product.image);
                                        Image.debugLine("entry/src/main/ets/pages/CartPage.ets(64:19)", "entry");
                                        Image.width(60);
                                        Image.height(60);
                                        Image.objectFit(ImageFit.Contain);
                                        Image.backgroundColor('#FFFFFF');
                                        Image.onError(() => {
                                            console.error(`Failed to load image: ${item.product.image}`);
                                        });
                                    }, Image);
                                    // 商品图片
                                    Stack.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Column.create();
                                        Column.debugLine("entry/src/main/ets/pages/CartPage.ets(79:17)", "entry");
                                        Column.alignItems(HorizontalAlign.Start);
                                        Column.layoutWeight(1);
                                    }, Column);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Text.create(item.product.name);
                                        Text.debugLine("entry/src/main/ets/pages/CartPage.ets(80:19)", "entry");
                                        Text.fontSize(16);
                                        Text.fontWeight(FontWeight.Medium);
                                    }, Text);
                                    Text.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Text.create(`¥${item.product.price.toFixed(2)}`);
                                        Text.debugLine("entry/src/main/ets/pages/CartPage.ets(84:19)", "entry");
                                        Text.fontSize(14);
                                        Text.fontColor('#ff6b35');
                                        Text.margin({ top: 4 });
                                    }, Text);
                                    Text.pop();
                                    Column.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        // 数量控制和删除
                                        Column.create();
                                        Column.debugLine("entry/src/main/ets/pages/CartPage.ets(93:17)", "entry");
                                        // 数量控制和删除
                                        Column.alignItems(HorizontalAlign.End);
                                    }, Column);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        // 删除按钮
                                        Button.createWithLabel('删除');
                                        Button.debugLine("entry/src/main/ets/pages/CartPage.ets(95:19)", "entry");
                                        // 删除按钮
                                        Button.width(50);
                                        // 删除按钮
                                        Button.height(24);
                                        // 删除按钮
                                        Button.fontSize(10);
                                        // 删除按钮
                                        Button.backgroundColor('#ff4444');
                                        // 删除按钮
                                        Button.margin({ bottom: 8 });
                                        // 删除按钮
                                        Button.onClick(() => {
                                            this.cartViewModel.removeFromCart(item.product.id);
                                        });
                                    }, Button);
                                    // 删除按钮
                                    Button.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        // 数量控制
                                        Row.create();
                                        Row.debugLine("entry/src/main/ets/pages/CartPage.ets(106:19)", "entry");
                                        // 数量控制
                                        Row.justifyContent(FlexAlign.Center);
                                        // 数量控制
                                        Row.alignItems(VerticalAlign.Center);
                                    }, Row);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Button.createWithLabel('-');
                                        Button.debugLine("entry/src/main/ets/pages/CartPage.ets(107:21)", "entry");
                                        Button.width(32);
                                        Button.height(32);
                                        Button.fontSize(18);
                                        Button.fontWeight(FontWeight.Bold);
                                        Button.backgroundColor(item.quantity > 1 ? '#E41F19' : '#CCCCCC');
                                        Button.fontColor('#FFFFFF');
                                        Button.borderRadius(16);
                                        Button.enabled(item.quantity > 1);
                                        Button.onClick(() => {
                                            this.cartViewModel.updateQuantity(item.product.id, item.quantity - 1);
                                        });
                                    }, Button);
                                    Button.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Text.create(item.quantity.toString());
                                        Text.debugLine("entry/src/main/ets/pages/CartPage.ets(120:21)", "entry");
                                        Text.width(40);
                                        Text.height(32);
                                        Text.textAlign(TextAlign.Center);
                                        Text.fontSize(16);
                                        Text.margin({ left: 4, right: 4 });
                                        Text.border({ width: 1, color: '#E0E0E0' });
                                        Text.borderRadius(4);
                                    }, Text);
                                    Text.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Button.createWithLabel('+');
                                        Button.debugLine("entry/src/main/ets/pages/CartPage.ets(129:21)", "entry");
                                        Button.width(32);
                                        Button.height(32);
                                        Button.fontSize(18);
                                        Button.fontWeight(FontWeight.Bold);
                                        Button.backgroundColor('#E41F19');
                                        Button.fontColor('#FFFFFF');
                                        Button.borderRadius(16);
                                        Button.onClick(() => {
                                            this.cartViewModel.updateQuantity(item.product.id, item.quantity + 1);
                                        });
                                    }, Button);
                                    Button.pop();
                                    // 数量控制
                                    Row.pop();
                                    // 数量控制和删除
                                    Column.pop();
                                    Row.pop();
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.cartItems, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    // 购物车列表
                    List.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部结算栏
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/CartPage.ets(154:9)", "entry");
                        // 底部结算栏
                        Row.width('100%');
                        // 底部结算栏
                        Row.height(80);
                        // 底部结算栏
                        Row.padding({ left: 16, right: 16 });
                        // 底部结算栏
                        Row.backgroundColor('#fff');
                        // 底部结算栏
                        Row.border({ width: { top: 1 }, color: '#e0e0e0' });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/CartPage.ets(155:11)", "entry");
                        Column.layoutWeight(1);
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`总计: ¥${this.cartViewModel.getTotalPrice().toFixed(2)}`);
                        Text.debugLine("entry/src/main/ets/pages/CartPage.ets(156:13)", "entry");
                        Text.fontSize(18);
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor('#ff6b35');
                    }, Text);
                    Text.pop();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('结算');
                        Button.debugLine("entry/src/main/ets/pages/CartPage.ets(164:11)", "entry");
                        Button.width(100);
                        Button.height(40);
                        Button.backgroundColor('#ff6b35');
                        Button.onClick(() => {
                            // 清空购物车
                            this.cartViewModel.clearCart();
                            // 跳转到结算成功页面
                            router.pushUrl({
                                url: 'pages/CheckoutSuccessPage'
                            });
                        });
                    }, Button);
                    Button.pop();
                    // 底部结算栏
                    Row.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "CartPage";
    }
}
registerNamedRoute(() => new CartPage(undefined, {}), "", { bundleName: "com.example.shi_yan_3", moduleName: "entry", pagePath: "pages/CartPage", pageFullPath: "entry/src/main/ets/pages/CartPage", integratedHsp: "false", moduleType: "followWithHap" });
