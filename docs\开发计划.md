# **开发计划书 - 简易购物平台**

**版本：V1.0**

**目标：** 本文档旨在将《需求分析书 V1.1》和《项目架构设计书 V1.0》转化为一份清晰、可执行、可跟踪的开发任务清单。

---

## **阶段一：项目初始化与架构骨架搭建**

**目标：** 严格按照架构设计，创建标准的项目目录结构和空文件，为后续功能开发奠定坚实基础。

- `[✅]` **任务 1.1：创建核心目录结构**
    - **描述：** 根据 `项目架构.md`，在 `entry/src/main/ets/` 路径下创建 `common`, `model`, `service`, `view`, `viewmodel` 目录。
    - **验证：** 检查目录结构是否与架构文档完全一致。

- `[✅]` **任务 1.2：创建模型（Model）层文件**
    - **描述：** 在 `model/` 目录下创建以下空的 TypeScript 接口文件：
        - `Product.ts`
        - `User.ts`
        - `CartItem.ts`
        - `DataSource.ts` (用于存放硬编码的商品数据)
    - **验证：** 确认所有模型文件均已创建。

- `[✅]` **任务 1.3：创建服务（Service）与常量文件**
    - **描述：** 创建以下文件：
        - `service/UserService.ts`
        - `common/Constants.ts`
    - **验证：** 确认文件已创建在正确的位置。

- `[✅]` **任务 1.4：创建视图模型（ViewModel）层文件**
    - **描述：** 在 `viewmodel/` 目录下创建以下空的 TypeScript 文件：
        - `UserViewModel.ts`
        - `CartViewModel.ts`
    - **验证：** 确认所有 ViewModel 文件均已创建。

- `[✅]` **任务 1.5：创建视图（View）层文件**
    - **描述：**
        - 在 `pages/` 目录下创建空的页面文件：`CartPage.ets`, `DetailsPage.ets`, `CheckoutSuccessPage.ets`。
        - 在 `view/` 目录下创建空的可复用组件文件：`ProductCard.ets`, `CartItemView.ets`, `BottomTabs.ets`。
    - **验证：** 确认所有新的页面和组件文件均已创建。

- `[✅]` **任务 1.6：👤 现有页面重构与重命名**
    - **描述：** 为符合架构规范，对现有页面进行重命名和移动：
        - 将 `entry/src/main/ets/pages/log_in.ets` 重命名并移动到 `entry/src/main/ets/pages/LoginPage.ets`。
        - 将 `entry/src/main/ets/pages/reg.ets` 重命名并移动到 `entry/src/main/ets/pages/RegisterPage.ets`。
    - **依赖：** 无。
    - **验证：** 确认文件已重命名并移动，项目能够正常编译。

---

## **阶段二：用户中心模块 (核心数据持久化)**

**目标：** 实现完整的用户注册、登录功能，并建立全局的、持久化的用户状态管理机制。

- `[✅]` **任务 2.1：用户注册/登录UI界面**
    - **描述：** 用户注册和登录的基础UI界面已存在。
    - **现状：** `RegisterPage.ets` 和 `LoginPage.ets` 已提供输入框和按钮。
    - **待办：** 后续任务将把业务逻辑从View层剥离到ViewModel。

- `[✅]` **任务 2.2：实现用户服务 `UserService`**
    - **描述：** 在 `service/UserService.ts` 中封装对 `@ohos.data.preferences` 的所有操作，实现用户数据的持久化存取。
    - **子任务：**
        - `[✅]` 实现 `registerUser(user)` 方法，将用户对象（序列化为JSON字符串）存入Preferences。
        - `[✅]` 实现 `login(username, password)` 方法，从Preferences中异步读取、反序列化并匹配用户信息。
        - `[ ]` 编写 `UserService` 的单元测试。
    - **依赖：** 任务 1.3。
    - **验证：** 单元测试通过，能成功读写用户信息。

- `[✅]` **任务 2.3：实现用户视图模型 `UserViewModel`**
    - **描述：** 在 `viewmodel/UserViewModel.ts` 中处理注册和登录的业务逻辑。
    - **子任务：**
        - `[✅]` 创建 `register` 方法，调用 `UserService.registerUser`。
        - `[✅]` 创建 `login` 方法，调用 `UserService.login`。
        - `[✅]` 登录成功后，调用下一步任务中定义的 `AppStorage` 更新方法。
    - **依赖：** 任务 2.2。
    - **验证：** 逻辑清晰，能正确调用Service层方法。

- `[✅]` **任务 2.4：重构注册与登录页面**
    - **描述：** 将 `RegisterPage.ets` 和 `LoginPage.ets` 中的业务逻辑（如校验、跳转）全部迁移到 `UserViewModel`。
    - **子任务：**
        - `[ ]` `RegisterPage.ets` 的“注册”按钮点击事件绑定到 `UserViewModel.handleRegister`。
        - `[ ]` `LoginPage.ets` 的“登录”按钮点击事件绑定到 `UserViewModel.handleLogin`。
        - `[ ]` 页面通过 `@State` 管理输入框内容，通过 `@Link` 或其他方式与ViewModel交互。
    - **依赖：** 任务 2.1, 2.3。
    - **验证：** 👤 手动测试注册和登录流程，确认功能与之前一致，但代码已遵循MVVM模式。

- `[✅]` **任务 2.5：实现全局登录状态管理**
    - **描述：** 使用 `AppStorage` 实现跨页面的登录状态同步。
    - **子任务：**
        - `[ ]` 在 `common/Constants.ts` 中定义 `AppStorage` 的Key，如 `KEY_IS_LOGGED_IN`, `KEY_CURRENT_USER`。
        - `[ ]` 在 `UserViewModel` 的 `handleLogin` 成功后，设置 `AppStorage.set(KEY_IS_LOGGED_IN, true)` 和 `AppStorage.set(KEY_CURRENT_USER, username)`。
        - `[ ]` 在 `Index.ets` 首页，使用 `@StorageLink` 监听登录状态，并更新UI（例如，将“欢迎您, 请登录”变为“欢迎您, [用户名]”）。
    - **依赖：** 任务 2.3。
    - **验证：** 👤 登录后，返回首页，欢迎语自动更新。刷新预览器后，状态依然保持（如果ViewModel在启动时检查了登录状态）。

---

## **阶段三：购物流程模块 (核心运行时状态)**

**目标：** 构建完整的商品浏览、购物车管理和模拟结算流程。

- `[✅]` **任务 3.1：首页商品列表UI**
    - **描述：** 首页已能分类展示硬编码的商品列表。
    - **现状：** `Index.ets` 已实现。
    - **待办：** 后续任务将为列表项添加交互功能。

- `[✅]` **任务 3.2：实现全局购物车 `CartViewModel`**
    - **描述：** 在 `viewmodel/CartViewModel.ts` 中封装对 `AppStorage` 中购物车数据的所有操作。
    - **子任务：**
        - `[✅]` 在 `common/Constants.ts` 中定义购物车Key，如 `SHOPPING_CART`。
        - `[✅]` 在应用启动时（如 `EntryAbility.ets`），初始化 `AppStorage.setOrCreate(SHOPPING_CART, [])`。
        - `[✅]` 在 `CartViewModel` 中实现 `addToCart`, `removeFromCart`, `updateQuantity`, `clearCart`, `getTotalQuantity` 等方法。
    - **依赖：** 任务 1.4。
    - **验证：** ViewModel方法能正确地增、删、改 `AppStorage` 中的购物车数组。

- `[✅]` **任务 3.3：实现商品详情页 `DetailsPage`**
    - **描述：** 创建并实现商品详情页。
    - **子任务：**
        - `[✅]` 完善 `DetailsPage.ets` 的UI布局，展示商品大图、描述、价格等。
        - `[✅]` 实现从 `Index.ets` 点击商品卡片跳转到对应 `DetailsPage.ets` 的路由逻辑。
        - `[✅]` 详情页的“加入购物车”按钮绑定 `CartViewModel.addToCart` 方法。
    - **依赖：** 任务 1.5, 3.2。
    - **验证：** 👤 能从首页进入详情页，并成功添加商品到购物车。

- `[✅]` **任务 3.4：实现“加入购物车”功能 (首页)**
    - **描述：** 为首页商品列表的“+”按钮添加功能。
    - **子任务：**
        - `[ ]` 将 `Index.ets` 中的商品列表项提取为可复用的 `view/ProductCard.ets` 组件。
        - `[ ]` `ProductCard.ets` 的“+”按钮点击事件绑定到 `CartViewModel.addToCart` 方法。
    - **依赖：** 任务 3.1, 3.2。
    - **验证：** 👤 在首页点击“+”，购物车数据正确更新。

- `[✅]` **任务 3.5：实现底部导航栏 `BottomTabs`**
    - **描述：** 创建应用的主导航框架，并集成购物车角标。
    - **子任务：**
        - `[ ]` 实现 `view/BottomTabs.ets` 组件，包含“首页”和“购物车”两个Tab。
        - `[ ]` 将 `BottomTabs.ets` 设置为应用的主入口页面。
        - `[ ]` “购物车”Tab的图标上使用 `Badge` 组件，其 `value` 绑定 `CartViewModel.getCartItemCount` 的计算结果。
    - **依赖：** 任务 1.5, 3.2。
    - **验证：** 👤 添加商品时，购物车角标数字实时增加。可自由切换首页和购物车页面。

- `[✅]` **任务 3.6：实现购物车页面 `CartPage`**
    - **描述：** 创建并实现功能完善的购物车管理页面。
    - **子任务：**
        - `[ ]` `CartPage.ets` 使用 `@StorageLink` 监听购物车数据并展示列表。
        - `[ ]` 将列表项提取为可复用的 `view/CartItemView.ets` 组件。
        - `[ ]` 在 `CartItemView.ets` 中实现数量调整、删除单个商品的功能，并绑定到 `CartViewModel` 的方法。
        - `[ ]` 页面显示购物车总价。
        - `[ ]` “去结算”按钮准备就绪。
    - **依赖：** 任务 1.5, 3.2, 3.5。
    - **验证：** 👤 能在购物车页面正确展示商品，并进行增、删、改操作，总价实时更新。

- `[✅]` **任务 3.7：实现模拟结算流程**
    - **描述：** 完成购物流程的闭环。
    - **子任务：**
        - `[ ]` “去结算”按钮点击后，调用 `CartViewModel.clearCart` 方法。
        - `[ ]` 清空购物车后，使用 `router` 跳转到 `CheckoutSuccessPage.ets`。
        - `[ ]` `CheckoutSuccessPage.ets` 展示一个简单的结算成功提示。
    - **依赖：** 任务 3.6。
    - **验证：** 👤 点击结算后，购物车被清空，并成功跳转到成功页面。

---

## **阶段四：优化与完善**

**目标：** 提升应用整体的UI/UX体验和代码质量。

- `[ ]` **任务 4.1：👤 UI风格统一与优化**
    - **描述：** 调整UI细节，确保应用整体风格一致、美观。
    - **子任务：**
        - `[ ]` 调整 `RegisterPage.ets` 的背景图样式，使其与其他页面的简洁风格保持一致。
        - `[ ]` 审视所有页面的边距、颜色、字体，进行微调。
    - **验证：** 视觉评审通过，所有页面风格协调统一。

- `[ ]` **任务 4.2：👤 添加过渡动画**
    - **描述：** 在关键交互点添加平滑的动画效果。
    - **子任务：**
        - `[ ]` 为页面跳转添加过渡动画。
        - `[ ]` 为购物车商品增删等列表变化添加动画。
    - **验证：** 交互体验评审通过，动画效果流畅自然。

- `[ ]` **任务 4.3：👤 最终代码审查与清理**
    - **描述：** 进行最终的代码质量检查。
    - **子任务：**
        - `[ ]` 检查所有代码是否严格遵循MVVM模式。
        - `[ ]` 删除所有调试用的 `console.log` 和未使用的代码。
        - `[ ]` 确保所有代码注释清晰、准确。
    - **验证：** 代码审查通过。

**后续补充改进**
[] 1. 商品详细页的数量加减按钮两个都是减号
[] 2. 购物车的数量加减按钮没有加减号，只有两个蓝色的圆圈，而且数量不能加只能减