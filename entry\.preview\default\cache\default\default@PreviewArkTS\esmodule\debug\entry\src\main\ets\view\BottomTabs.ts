if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BottomTabs_Params {
    cartItems?: CartItem[];
    currentIndex?: number;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/common/Constants&";
import type { CartItem } from '../model/CartItem';
import { IndexContent } from "@normalized:N&&&entry/src/main/ets/view/IndexContent&";
import { CartContent } from "@normalized:N&&&entry/src/main/ets/view/CartContent&";
class BottomTabs extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cartItems = this.createStorageLink(Constants.SHOPPING_CART, [], "cartItems");
        this.__currentIndex = new ObservedPropertySimplePU(0, this, "currentIndex");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BottomTabs_Params) {
        if (params.currentIndex !== undefined) {
            this.currentIndex = params.currentIndex;
        }
    }
    updateStateVars(params: BottomTabs_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cartItems.purgeDependencyOnElmtId(rmElmtId);
        this.__currentIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cartItems.aboutToBeDeleted();
        this.__currentIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cartItems: ObservedPropertyAbstractPU<CartItem[]>;
    get cartItems() {
        return this.__cartItems.get();
    }
    set cartItems(newValue: CartItem[]) {
        this.__cartItems.set(newValue);
    }
    private __currentIndex: ObservedPropertySimplePU<number>;
    get currentIndex() {
        return this.__currentIndex.get();
    }
    set currentIndex(newValue: number) {
        this.__currentIndex.set(newValue);
    }
    /**
     * 计算购物车中商品总数量
     */
    private getCartTotalQuantity(): number {
        return this.cartItems.reduce((total, item) => total + item.quantity, 0);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Tabs.create({ barPosition: BarPosition.End, index: this.currentIndex });
            Tabs.debugLine("entry/src/main/ets/view/BottomTabs.ets(23:5)", "entry");
            Tabs.onChange((index: number) => {
                this.currentIndex = index;
            });
        }, Tabs);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new IndexContent(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/view/BottomTabs.ets", line: 26, col: 9 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {};
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {});
                        }
                    }, { name: "IndexContent" });
                }
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBuilder.call(this, '首页', 0, { "id": 125831533, "type": 40000, params: [], "bundleName": "com.example.shi_yan_3", "moduleName": "entry" });
                } });
            TabContent.debugLine("entry/src/main/ets/view/BottomTabs.ets(25:7)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new CartContent(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/view/BottomTabs.ets", line: 32, col: 9 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {};
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {});
                        }
                    }, { name: "CartContent" });
                }
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBuilder.call(this, '购物车', 1, { "id": 125832323, "type": 40000, params: [], "bundleName": "com.example.shi_yan_3", "moduleName": "entry" }, this.getCartTotalQuantity());
                } });
            TabContent.debugLine("entry/src/main/ets/view/BottomTabs.ets(31:7)", "entry");
        }, TabContent);
        TabContent.pop();
        Tabs.pop();
    }
    /**
     * 构建标签栏项
     */
    TabBuilder(title: string, targetIndex: number, icon: Resource, badgeCount?: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/view/BottomTabs.ets(46:5)", "entry");
            Column.width('100%');
            Column.height(56);
            Column.justifyContent(FlexAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/view/BottomTabs.ets(47:7)", "entry");
            Stack.margin({ bottom: 4 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(icon);
            Image.debugLine("entry/src/main/ets/view/BottomTabs.ets(48:9)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor(this.currentIndex === targetIndex ? '#ff6b35' : '#999');
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 购物车徽章
            if (badgeCount && badgeCount > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(badgeCount > 99 ? '99+' : badgeCount.toString());
                        Text.debugLine("entry/src/main/ets/view/BottomTabs.ets(55:11)", "entry");
                        Text.fontSize(10);
                        Text.fontColor('#fff');
                        Text.backgroundColor('#E41F19');
                        Text.borderRadius(10);
                        Text.padding({ left: 6, right: 6, top: 2, bottom: 2 });
                        Text.position({ x: 14, y: -6 });
                        Text.width(20);
                        Text.textAlign(TextAlign.Center);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/view/BottomTabs.ets(68:7)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.currentIndex === targetIndex ? '#ff6b35' : '#999');
        }, Text);
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BottomTabs";
    }
}
registerNamedRoute(() => new BottomTabs(undefined, {}), "", { bundleName: "com.example.shi_yan_3", moduleName: "entry", pagePath: "view/BottomTabs", pageFullPath: "entry/src/main/ets/view/BottomTabs", integratedHsp: "false", moduleType: "followWithHap" });
