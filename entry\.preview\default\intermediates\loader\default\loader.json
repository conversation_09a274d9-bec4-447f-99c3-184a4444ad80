{"modulePathMap": {"entry": "D:\\Code\\harmony\\harmony_shop\\entry"}, "compileMode": "esmodule", "projectRootPath": "D:\\Code\\harmony\\harmony_shop", "nodeModulesPath": "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\default\\intermediates\\loader_out\\default\\node_modules", "byteCodeHarInfo": {}, "declarationEntry": [], "moduleName": "entry", "hspNameOhmMap": {}, "harNameOhmMap": {}, "packageManagerType": "ohpm", "compileEntry": [], "otherCompileFiles": [], "dynamicImportLibInfo": {}, "routerMap": [], "hspResourcesMap": {}, "updateVersionInfo": {}, "anBuildOutPut": "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\default\\intermediates\\loader_out\\default\\an\\arm64-v8a", "anBuildMode": "type", "buildConfigPath": ".preview\\config\\buildConfig.json"}