{"app": {"bundleName": "com.example.shi_yan_3", "vendor": "example", "versionCode": 1000000, "versionName": "1.0.0", "icon": "$media:app_icon", "label": "$string:app_name", "apiReleaseType": "Release", "compileSdkVersion": "*********", "targetAPIVersion": 50005017, "minAPIVersion": 50002014, "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug", "debug": true, "iconId": 16777218, "labelId": 16777216}, "module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet", "2in1"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:layered_image", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:startIcon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}], "descriptionId": 16777232, "iconId": 16777225, "labelId": 16777233, "startWindowIconId": 16777226, "startWindowBackgroundId": 16777224, "ohmurl": "@normalized:N&&&entry/src/main/ets/entryability/EntryAbility&"}], "extensionAbilities": [{"name": "EntryBackupAbility", "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets", "type": "backup", "exported": false, "metadata": [{"name": "ohos.extension.backup", "resource": "$profile:backup_config", "resourceId": 16777228}], "ohmurl": "@normalized:N&&&entry/src/main/ets/entrybackupability/EntryBackupAbility&"}], "packageName": "entry", "virtualMachine": "ark12.0.6.0", "compileMode": "esmodule", "dependencies": [], "descriptionId": 16777234}}