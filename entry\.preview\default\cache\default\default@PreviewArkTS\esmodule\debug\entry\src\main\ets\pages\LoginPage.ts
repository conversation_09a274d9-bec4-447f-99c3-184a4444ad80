if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LoginPage_Params {
    username?: string;
    password?: string;
    viewModel?: UserViewModel;
    context?;
}
import router from "@ohos:router";
import { UserViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/UserViewModel&";
import promptAction from "@ohos:promptAction";
import type common from "@ohos:app.ability.common";
class LoginPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__username = new ObservedPropertySimplePU('', this, "username");
        this.__password = new ObservedPropertySimplePU('', this, "password");
        this.viewModel = new UserViewModel();
        this.context = getContext(this) as common.UIAbilityContext;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LoginPage_Params) {
        if (params.username !== undefined) {
            this.username = params.username;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
        if (params.viewModel !== undefined) {
            this.viewModel = params.viewModel;
        }
        if (params.context !== undefined) {
            this.context = params.context;
        }
    }
    updateStateVars(params: LoginPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__username.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__username.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __username: ObservedPropertySimplePU<string>;
    get username() {
        return this.__username.get();
    }
    set username(newValue: string) {
        this.__username.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>;
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    private viewModel: UserViewModel;
    private context;
    async aboutToAppear() {
        // 初始化 UserViewModel
        await this.viewModel.init(this.context);
    }
    LoginPageContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(23:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.justifyContent(FlexAlign.Start);
            Column.alignItems(HorizontalAlign.Center);
            Column.backgroundColor('#F1F3F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create('https://image2.135editor.com/cache/remote/aHR0cHM6Ly9tbWJpei5xbG9nby5jbi9tbWJpel9qcGcvQUlKZ00zYmt3OG03SGhtb3IxRzdYMzZEVkFjMThpYVhHaWFUaWJPaENVSGpsd3JYVmdMU1pRSWFYTEloZmhZYXByVXJnTVAxV2pFb1I2aWNmRXRwd25jb0N3LzY0MC5qcGVn');
            Image.debugLine("entry/src/main/ets/pages/LoginPage.ets(24:7)", "entry");
            Image.width(100);
            Image.height(100);
            Image.borderRadius(20);
            Image.margin({ top: 100, bottom: 50 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '用户名' });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(30:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.margin(10);
            TextInput.backgroundColor(Color.White);
            TextInput.onChange((value) => {
                this.username = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '密码' });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(39:7)", "entry");
            TextInput.type(InputType.Password);
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.margin(10);
            TextInput.backgroundColor(Color.White);
            TextInput.onChange((value) => {
                this.password = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('登录');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(49:7)", "entry");
            Button.width('60%');
            Button.height(50);
            Button.margin(30);
            Button.onClick(async () => {
                const result = await this.viewModel.login(this.username, this.password);
                if (result.success) {
                    promptAction.showToast({ message: result.message });
                    router.replaceUrl({ url: 'view/BottomTabs' });
                }
                else {
                    promptAction.showToast({ message: result.message });
                }
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(63:7)", "entry");
            Row.margin({ top: 10 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('还没有账号？');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(64:9)", "entry");
            Text.fontColor(Color.Black);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('去注册');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(65:9)", "entry");
            Text.decoration({ type: TextDecorationType.Underline, color: Color.Blue });
            Text.fontColor(Color.Blue);
            Text.onClick(() => {
                router.replaceUrl({ url: 'pages/RegisterPage' });
            });
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(82:5)", "entry");
        }, Column);
        this.LoginPageContent.bind(this)();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "LoginPage";
    }
}
registerNamedRoute(() => new LoginPage(undefined, {}), "", { bundleName: "com.example.shi_yan_3", moduleName: "entry", pagePath: "pages/LoginPage", pageFullPath: "entry/src/main/ets/pages/LoginPage", integratedHsp: "false", moduleType: "followWithHap" });
