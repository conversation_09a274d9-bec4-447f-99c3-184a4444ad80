import { Product } from '../model/Product';
import { DataSource } from '../model/DataSource';
import { CartViewModel } from '../viewmodel/CartViewModel';
import { Constants } from '../common/Constants';
import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

/**
 * 商品详情页
 */
@Entry
@Component
struct DetailsPage {
  @State product: Product | undefined = undefined;
  @State quantity: number = 1;
  @StorageLink(Constants.IS_LOGGED_IN) isLoggedIn: boolean = false;
  private cartViewModel: CartViewModel = new CartViewModel();

  aboutToAppear() {
    // 获取路由参数
    const params = router.getParams() as Record<string, Object>;
    if (params && params.productId) {
      this.product = DataSource.getProductById(params.productId as number);
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button({ type: ButtonType.Circle }) {
          Text('←')
            .fontSize(20)
            .fontColor('#333')
        }
        .width(40)
        .height(40)
        .backgroundColor('#F5F5F5')
        .onClick(() => {
          router.back();
        })

        Blank()

        Text('商品详情')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333')

        Blank()

        // 占位，保持居中
        Text('')
          .width(40)
          .height(40)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#fff')
      .shadow({ radius: 4, color: 'rgba(0, 0, 0, 0.1)', offsetY: 2 })

      if (this.product) {
        Scroll() {
          Column() {
            // 商品图片
            Stack({ alignContent: Alignment.Center }) {
              Image(this.product.image)
                .width('100%')
                .height(300)
                .objectFit(ImageFit.Contain)
                .backgroundColor('#FFFFFF')
                .onError(() => {
                  console.error(`Failed to load image: ${this.product?.image}`);
                })
            }
            .width('100%')
            .height(300)
            .backgroundColor('#F9F9F9')
            .borderRadius(8)

            // 商品信息
            Column() {
              Text(this.product.name)
                .fontSize(20)
                .fontWeight(FontWeight.Bold)
                .margin({ bottom: 12 })
                .textAlign(TextAlign.Start)
                .width('100%')

              Row() {
                Text(`¥${this.product.price.toFixed(2)}`)
                  .fontSize(28)
                  .fontColor('#E41F19')
                  .fontWeight(FontWeight.Bold)

                Blank()

                Text('现货')
                  .fontSize(12)
                  .fontColor('#4CAF50')
                  .backgroundColor('#E8F5E8')
                  .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                  .borderRadius(4)
              }
              .width('100%')
              .margin({ bottom: 16 })

              // 商品特色标签
              Row({ space: 8 }) {
                Text('正品保证')
                  .fontSize(10)
                  .fontColor('#E41F19')
                  .border({ width: 1, color: '#E41F19' })
                  .padding({ left: 6, right: 6, top: 2, bottom: 2 })
                  .borderRadius(2)

                Text('7天退换')
                  .fontSize(10)
                  .fontColor('#E41F19')
                  .border({ width: 1, color: '#E41F19' })
                  .padding({ left: 6, right: 6, top: 2, bottom: 2 })
                  .borderRadius(2)

                Text('全国包邮')
                  .fontSize(10)
                  .fontColor('#E41F19')
                  .border({ width: 1, color: '#E41F19' })
                  .padding({ left: 6, right: 6, top: 2, bottom: 2 })
                  .borderRadius(2)
              }
              .width('100%')
              .margin({ bottom: 20 })

              if (this.product.description) {
                Text('商品详情')
                  .fontSize(16)
                  .fontWeight(FontWeight.Medium)
                  .margin({ bottom: 8 })
                  .textAlign(TextAlign.Start)
                  .width('100%')

                Text(this.product.description)
                  .fontSize(14)
                  .fontColor('#666')
                  .lineHeight(22)
                  .textAlign(TextAlign.Start)
                  .width('100%')
              }
            }
            .width('100%')
            .padding(16)
            .alignItems(HorizontalAlign.Start)
            .backgroundColor('#fff')
            .margin({ top: 8 })
            .borderRadius(8)

            // 数量选择和规格
            Column() {
              // 数量选择
              Row() {
                Text('数量:')
                  .fontSize(16)
                  .fontWeight(FontWeight.Medium)
                  .margin({ right: 16 })

                Row() {
                  Button('-')
                    .width(36)
                    .height(36)
                    .fontSize(18)
                    .backgroundColor(this.quantity > 1 ? '#E41F19' : '#CCCCCC')
                    .fontColor('#FFFFFF')
                    .enabled(this.quantity > 1)
                    .onClick(() => {
                      if (this.quantity > 1) {
                        this.quantity--;
                      }
                    })

                  Text(this.quantity.toString())
                    .width(50)
                    .textAlign(TextAlign.Center)
                    .fontSize(16)
                    .margin({ left: 8, right: 8 })
                    .border({ width: 1, color: '#E0E0E0' })
                    .height(36)

                  Button('+')
                    .width(36)
                    .height(36)
                    .fontSize(18)
                    .backgroundColor('#E41F19')
                    .fontColor('#FFFFFF')
                    .onClick(() => {
                      this.quantity++;
                    })
                }
                .alignItems(VerticalAlign.Center)
              }
              .width('100%')
              .margin({ bottom: 16 })
              .justifyContent(FlexAlign.Start)

              // 配送信息
              Row() {
                Text('配送:')
                  .fontSize(16)
                  .fontWeight(FontWeight.Medium)
                  .margin({ right: 16 })

                Text('全国包邮 · 预计1-3天到达')
                  .fontSize(14)
                  .fontColor('#666')
              }
              .width('100%')
              .justifyContent(FlexAlign.Start)
            }
            .width('100%')
            .padding(16)
            .backgroundColor('#fff')
            .margin({ top: 8 })
            .alignItems(HorizontalAlign.Start)
            .borderRadius(8)
          }
        }
        .layoutWeight(1)

        // 底部操作栏
        Row() {
          Button('加入购物车')
            .layoutWeight(1)
            .height(50)
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .backgroundColor('#FFA500')
            .fontColor('#FFFFFF')
            .margin({ right: 8 })
            .borderRadius(25)
            .onClick(() => {
              if (!this.isLoggedIn) {
                promptAction.showToast({
                  message: '请先登录后再加入购物车',
                  duration: 2000
                });
                router.pushUrl({ url: 'pages/LoginPage' });
                return;
              }

              if (this.product) {
                this.cartViewModel.addToCart(this.product, this.quantity);
                promptAction.showToast({
                  message: `已将 ${this.product.name} 加入购物车`,
                  duration: 2000
                });
              }
            })

          Button('立即购买')
            .layoutWeight(1)
            .height(50)
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .backgroundColor('#E41F19')
            .fontColor('#FFFFFF')
            .margin({ left: 8 })
            .borderRadius(25)
            .onClick(() => {
              if (!this.isLoggedIn) {
                promptAction.showToast({
                  message: '请先登录后再购买',
                  duration: 2000
                });
                router.pushUrl({ url: 'pages/LoginPage' });
                return;
              }

              if (this.product) {
                this.cartViewModel.addToCart(this.product, this.quantity);
                router.pushUrl({
                  url: 'pages/CartPage'
                });
              }
            })
        }
        .width('100%')
        .padding(16)
        .backgroundColor('#fff')
        .shadow({ radius: 8, color: 'rgba(0, 0, 0, 0.1)', offsetY: -2 })
      } else {
        // 商品不存在
        Column() {
          Text('商品不存在')
            .fontSize(16)
            .fontColor('#999')
          
          Button('返回')
            .margin({ top: 20 })
            .onClick(() => {
              router.back();
            })
        }
        .width('100%')
        .height('60%')
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5')
  }
}
