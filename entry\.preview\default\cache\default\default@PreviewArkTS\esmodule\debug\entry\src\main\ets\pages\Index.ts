if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ShoppingPage_Params {
    isLoggedIn?: boolean;
    currentUser?: string;
    selectedCategory?: string;
    categories?: string[];
    cartViewModel?: CartViewModel;
    userViewModel?: UserViewModel;
    allProducts?: IndexProduct[];
    filteredProducts?: IndexProduct[];
}
import router from "@ohos:router";
import { Constants } from "@normalized:N&&&entry/src/main/ets/common/Constants&";
import { CartViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/CartViewModel&";
import { UserViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/UserViewModel&";
import type { Product } from '../model/Product';
import promptAction from "@ohos:promptAction";
interface IndexProduct {
    id: number;
    name: string;
    imageUrl: string;
    price: string;
    category: string;
}
class ShoppingPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isLoggedIn = this.createStorageLink(Constants.IS_LOGGED_IN, false, "isLoggedIn");
        this.__currentUser = this.createStorageLink(Constants.CURRENT_USER, '', "currentUser");
        this.__selectedCategory = new ObservedPropertySimplePU('手机', this, "selectedCategory");
        this.categories = ['手机', '鞋子', '衣服'];
        this.cartViewModel = new CartViewModel();
        this.userViewModel = new UserViewModel();
        this.__allProducts = new ObservedPropertyObjectPU([
            {
                id: 1,
                name: 'Apple iPhone 16 Pro Max 256GB',
                imageUrl: 'https://img10.360buyimg.com/n5/s720x720_jfs/t1/168145/19/47417/9061/66df731fF3d55d94e/ce6d11f80a757287.jpg',
                price: '¥7999',
                category: '手机'
            },
            {
                id: 2,
                name: '小米 15 白色',
                imageUrl: 'https://img10.360buyimg.com/n5/s720x720_jfs/t1/188918/24/49838/43069/67203b9aFea4cec24/563909f5ba6329ea.jpg',
                price: '¥4999',
                category: '手机'
            },
            {
                id: 3,
                name: '三星Samsung Galaxy S24 Ultra',
                imageUrl: 'https://img13.360buyimg.com/n5/s720x720_jfs/t1/196045/16/45365/38717/664d6022Fe03b017b/40444f6ba5878a5e.jpg',
                price: '¥6466',
                category: '手机'
            },
            {
                id: 4,
                name: 'HUAWEI Mate 70',
                imageUrl: 'https://img12.360buyimg.com/n5/s720x720_jfs/t1/242313/11/23691/25932/6747e3e8F41f778d7/294a54279bf82296.jpg',
                price: '¥12999',
                category: '手机'
            },
            {
                id: 5,
                name: '安踏（ANTA）男鞋板鞋',
                imageUrl: 'https://img13.360buyimg.com/n5/s720x720_jfs/t1/257962/11/1272/79927/67668593F28507096/82e67149a95c0600.jpg',
                price: '¥149',
                category: '鞋子'
            },
            {
                id: 6,
                name: '李宁赤兔6Pro新款跑步鞋',
                imageUrl: 'https://img11.360buyimg.com/n5/s720x720_jfs/t1/234541/28/7960/235110/65792a01Fe4787218/4a265d5a1ca4e488.jpg',
                price: '¥399',
                category: '鞋子'
            },
            {
                id: 7,
                name: '回力（Warrior）夏季休闲鞋',
                imageUrl: 'https://img11.360buyimg.com/n5/s720x720_jfs/t1/282321/26/7024/79106/67de5dcbF7f21af05/1fbd2ba6713b0f57.jpg',
                price: '¥99',
                category: '鞋子'
            },
            {
                id: 8,
                name: '回力纯棉舒适T恤',
                imageUrl: 'https://img12.360buyimg.com/n5/s720x720_jfs/t1/216377/40/39529/39335/6616830aFec86cb26/3def54baa7b784f1.jpg',
                price: '¥59',
                category: '衣服'
            },
            {
                id: 9,
                name: '真维斯白色卫衣男连帽',
                imageUrl: 'https://img.alicdn.com/imgextra/i1/2217457947858/O1CN01E2MvxE27v1I1gddDg_!!2217457947858.jpg',
                price: '¥99',
                category: '衣服'
            },
            {
                id: 10,
                name: 'WASSUP ERIKA潮牌男装轻熟西装外套',
                imageUrl: 'https://img.alicdn.com/imgextra/i2/3270715789/O1CN01kSFnJ81sdPqHwKcds_!!3270715789.jpg',
                price: '¥199',
                category: '衣服'
            },
        ], this, "allProducts");
        this.__filteredProducts = new ObservedPropertyObjectPU([], this, "filteredProducts");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ShoppingPage_Params) {
        if (params.selectedCategory !== undefined) {
            this.selectedCategory = params.selectedCategory;
        }
        if (params.categories !== undefined) {
            this.categories = params.categories;
        }
        if (params.cartViewModel !== undefined) {
            this.cartViewModel = params.cartViewModel;
        }
        if (params.userViewModel !== undefined) {
            this.userViewModel = params.userViewModel;
        }
        if (params.allProducts !== undefined) {
            this.allProducts = params.allProducts;
        }
        if (params.filteredProducts !== undefined) {
            this.filteredProducts = params.filteredProducts;
        }
    }
    updateStateVars(params: ShoppingPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isLoggedIn.purgeDependencyOnElmtId(rmElmtId);
        this.__currentUser.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCategory.purgeDependencyOnElmtId(rmElmtId);
        this.__allProducts.purgeDependencyOnElmtId(rmElmtId);
        this.__filteredProducts.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isLoggedIn.aboutToBeDeleted();
        this.__currentUser.aboutToBeDeleted();
        this.__selectedCategory.aboutToBeDeleted();
        this.__allProducts.aboutToBeDeleted();
        this.__filteredProducts.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    // 登录状态管理
    private __isLoggedIn: ObservedPropertyAbstractPU<boolean>;
    get isLoggedIn() {
        return this.__isLoggedIn.get();
    }
    set isLoggedIn(newValue: boolean) {
        this.__isLoggedIn.set(newValue);
    }
    private __currentUser: ObservedPropertyAbstractPU<string>;
    get currentUser() {
        return this.__currentUser.get();
    }
    set currentUser(newValue: string) {
        this.__currentUser.set(newValue);
    }
    // State for the selected category
    private __selectedCategory: ObservedPropertySimplePU<string>; // Default selected category
    get selectedCategory() {
        return this.__selectedCategory.get();
    }
    set selectedCategory(newValue: string) {
        this.__selectedCategory.set(newValue);
    }
    // List of categories
    private categories: string[];
    // 购物车视图模型
    private cartViewModel: CartViewModel;
    // 用户视图模型
    private userViewModel: UserViewModel;
    // All products data (replace with your actual data source)
    private __allProducts: ObservedPropertyObjectPU<IndexProduct[]>;
    get allProducts() {
        return this.__allProducts.get();
    }
    set allProducts(newValue: IndexProduct[]) {
        this.__allProducts.set(newValue);
    }
    // Filtered products based on selected category
    private __filteredProducts: ObservedPropertyObjectPU<IndexProduct[]>;
    get filteredProducts() {
        return this.__filteredProducts.get();
    }
    set filteredProducts(newValue: IndexProduct[]) {
        this.__filteredProducts.set(newValue);
    }
    // Lifecycle method: Called when the component is about to appear
    aboutToAppear() {
        this.updateFilteredProducts();
    }
    // Method to update the filtered products list
    updateFilteredProducts() {
        this.filteredProducts = this.allProducts.filter(product => product.category === this.selectedCategory);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(120:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 1. Top Title Bar with modern design
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/Index.ets(122:7)", "entry");
            // 1. Top Title Bar with modern design
            Row.width('100%');
            // 1. Top Title Bar with modern design
            Row.height(60);
            // 1. Top Title Bar with modern design
            Row.padding({ left: 20, right: 20 });
            // 1. Top Title Bar with modern design
            Row.backgroundColor('#FFFFFF');
            // 1. Top Title Bar with modern design
            Row.shadow({ radius: 8, color: 'rgba(0, 0, 0, 0.1)', offsetY: 2 });
            // 1. Top Title Bar with modern design
            Row.justifyContent(FlexAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('拼夕夕商店');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(123:9)", "entry");
            Text.fontSize(22);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#E41F19');
        }, Text);
        Text.pop();
        // 1. Top Title Bar with modern design
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 2. Welcome and Login Area
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/Index.ets(136:7)", "entry");
            // 2. Welcome and Login Area
            Row.width('100%');
            // 2. Welcome and Login Area
            Row.padding({ left: 20, right: 20, top: 15, bottom: 15 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('商品列表');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(137:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild({ type: ButtonType.Capsule });
            Button.debugLine("entry/src/main/ets/pages/Index.ets(143:9)", "entry");
            Button.height(36);
            Button.backgroundColor('#E41F19');
            Button.onClick(() => {
                if (!this.isLoggedIn) {
                    router.pushUrl({ url: 'pages/LoginPage' });
                }
                else {
                    // 显示退出登录确认对话框
                    AlertDialog.show({
                        title: '退出登录',
                        message: '确定要退出登录吗？',
                        primaryButton: {
                            value: '确定',
                            action: () => {
                                this.userViewModel.logout();
                                promptAction.showToast({
                                    message: '已退出登录',
                                    duration: 2000
                                });
                            }
                        },
                        secondaryButton: {
                            value: '取消',
                            action: () => { }
                        }
                    });
                }
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.isLoggedIn ? `欢迎您, ${this.currentUser}` : '欢迎您, 请登录');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(144:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        Button.pop();
        // 2. Welcome and Login Area
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 3. Category Navigation Bar with pill design
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/Index.ets(180:7)", "entry");
            // 3. Category Navigation Bar with pill design
            Scroll.scrollable(ScrollDirection.Horizontal);
            // 3. Category Navigation Bar with pill design
            Scroll.width('100%');
            // 3. Category Navigation Bar with pill design
            Scroll.height(60);
            // 3. Category Navigation Bar with pill design
            Scroll.margin({ bottom: 10 });
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
            Row.debugLine("entry/src/main/ets/pages/Index.ets(181:9)", "entry");
            Row.padding({ left: 20, right: 10 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const category = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Button.createWithChild({ type: ButtonType.Capsule });
                    Button.debugLine("entry/src/main/ets/pages/Index.ets(183:13)", "entry");
                    Button.height(40);
                    Button.padding({ left: 20, right: 20 });
                    Button.backgroundColor(this.selectedCategory === category ? '#E41F19' : '#F5F5F5');
                    Button.onClick(() => {
                        this.selectedCategory = category;
                        this.updateFilteredProducts();
                    });
                }, Button);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(category);
                    Text.debugLine("entry/src/main/ets/pages/Index.ets(184:15)", "entry");
                    Text.fontSize(16);
                    Text.fontColor(this.selectedCategory === category ? '#FFFFFF' : '#333333');
                }, Text);
                Text.pop();
                Button.pop();
            };
            this.forEachUpdateFunction(elmtId, this.categories, forEachItemGenFunction, (item: string) => item, false, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
        // 3. Category Navigation Bar with pill design
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 4. Product List with card design
            List.create({ space: 15 });
            List.debugLine("entry/src/main/ets/pages/Index.ets(205:7)", "entry");
            // 4. Product List with card design
            List.layoutWeight(1);
            // 4. Product List with card design
            List.padding({ left: 20, right: 20, bottom: 20 });
            // 4. Product List with card design
            List.backgroundColor('#F5F5F5');
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const product = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                        ListItem.width('100%');
                        ListItem.debugLine("entry/src/main/ets/pages/Index.ets(207:11)", "entry");
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Column.create();
                            Column.debugLine("entry/src/main/ets/pages/Index.ets(208:13)", "entry");
                            Column.backgroundColor(Color.White);
                            Column.borderRadius(12);
                            Column.shadow({ radius: 8, color: 'rgba(0, 0, 0, 0.05)', offsetY: 2 });
                            Column.onClick(() => {
                                // 跳转到商品详情页
                                router.pushUrl({
                                    url: 'pages/DetailsPage',
                                    params: { productId: product.id }
                                });
                            });
                        }, Column);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            // Product Image with better container
                            Stack.create({ alignContent: Alignment.Center });
                            Stack.debugLine("entry/src/main/ets/pages/Index.ets(210:15)", "entry");
                            // Product Image with better container
                            Stack.width('100%');
                            // Product Image with better container
                            Stack.height(200);
                            // Product Image with better container
                            Stack.backgroundColor('#F9F9F9');
                        }, Stack);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Image.create(product.imageUrl);
                            Image.debugLine("entry/src/main/ets/pages/Index.ets(211:17)", "entry");
                            Image.width('100%');
                            Image.height(200);
                            Image.objectFit(ImageFit.Contain);
                            Image.backgroundColor('#FFFFFF');
                            Image.onError(() => {
                                console.error(`Failed to load image: ${product.imageUrl}`);
                            });
                        }, Image);
                        // Product Image with better container
                        Stack.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            // Product Details with better spacing and typography
                            Column.create();
                            Column.debugLine("entry/src/main/ets/pages/Index.ets(225:15)", "entry");
                            // Product Details with better spacing and typography
                            Column.width('100%');
                            // Product Details with better spacing and typography
                            Column.padding({ left: 15, right: 15, bottom: 15, top: 5 });
                            // Product Details with better spacing and typography
                            Column.alignItems(HorizontalAlign.Start);
                        }, Column);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(product.name);
                            Text.debugLine("entry/src/main/ets/pages/Index.ets(226:17)", "entry");
                            Text.fontSize(16);
                            Text.fontWeight(FontWeight.Medium);
                            Text.maxLines(2);
                            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                            Text.margin({ bottom: 8 });
                            Text.padding({ top: 5 });
                        }, Text);
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Row.create();
                            Row.debugLine("entry/src/main/ets/pages/Index.ets(234:17)", "entry");
                            Row.width('100%');
                        }, Row);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(product.price);
                            Text.debugLine("entry/src/main/ets/pages/Index.ets(235:19)", "entry");
                            Text.fontSize(20);
                            Text.fontColor('#E41F19');
                            Text.fontWeight(FontWeight.Bold);
                        }, Text);
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Blank.create();
                            Blank.debugLine("entry/src/main/ets/pages/Index.ets(240:19)", "entry");
                        }, Blank);
                        Blank.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Button.createWithChild({ type: ButtonType.Circle, stateEffect: true });
                            Button.debugLine("entry/src/main/ets/pages/Index.ets(242:19)", "entry");
                            Button.width(36);
                            Button.height(36);
                            Button.backgroundColor('#E41F19');
                            Button.onClick(() => {
                                // 检查登录状态
                                if (!this.isLoggedIn) {
                                    promptAction.showToast({
                                        message: '请先登录后再加入购物车',
                                        duration: 2000
                                    });
                                    router.pushUrl({ url: 'pages/LoginPage' });
                                    return;
                                }
                                // 将商品添加到购物车
                                const productToAdd: Product = {
                                    id: product.id,
                                    name: product.name,
                                    price: parseFloat(product.price.replace('¥', '')),
                                    image: product.imageUrl,
                                    description: `${product.category}商品`
                                };
                                this.cartViewModel.addToCart(productToAdd, 1);
                                promptAction.showToast({
                                    message: `已将 ${product.name} 加入购物车`,
                                    duration: 2000
                                });
                            });
                        }, Button);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create('+');
                            Text.debugLine("entry/src/main/ets/pages/Index.ets(243:21)", "entry");
                            Text.fontSize(20);
                            Text.fontWeight(FontWeight.Bold);
                            Text.fontColor('#FFFFFF');
                        }, Text);
                        Text.pop();
                        Button.pop();
                        Row.pop();
                        // Product Details with better spacing and typography
                        Column.pop();
                        Column.pop();
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.filteredProducts, forEachItemGenFunction, (item: IndexProduct) => item.id.toString(), false, false);
        }, ForEach);
        ForEach.pop();
        // 4. Product List with card design
        List.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "ShoppingPage";
    }
}
registerNamedRoute(() => new ShoppingPage(undefined, {}), "", { bundleName: "com.example.shi_yan_3", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
