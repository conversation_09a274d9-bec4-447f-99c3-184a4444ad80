if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface CheckoutSuccessPage_Params {
    cartViewModel?: CartViewModel;
}
import { CartViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/CartViewModel&";
import router from "@ohos:router";
class CheckoutSuccessPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.cartViewModel = new CartViewModel();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: CheckoutSuccessPage_Params) {
        if (params.cartViewModel !== undefined) {
            this.cartViewModel = params.cartViewModel;
        }
    }
    updateStateVars(params: CheckoutSuccessPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private cartViewModel: CartViewModel;
    aboutToAppear() {
        // 页面初始化
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CheckoutSuccessPage.ets(17:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f5f5');
            Column.justifyContent(FlexAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 成功图标和文字
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CheckoutSuccessPage.ets(19:7)", "entry");
            // 成功图标和文字
            Column.justifyContent(FlexAlign.Center);
            // 成功图标和文字
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('✓');
            Text.debugLine("entry/src/main/ets/pages/CheckoutSuccessPage.ets(20:9)", "entry");
            Text.fontSize(80);
            Text.fontColor('#4CAF50');
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付成功！');
            Text.debugLine("entry/src/main/ets/pages/CheckoutSuccessPage.ets(26:9)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('您的订单已提交，我们会尽快为您处理');
            Text.debugLine("entry/src/main/ets/pages/CheckoutSuccessPage.ets(31:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 40 });
        }, Text);
        Text.pop();
        // 成功图标和文字
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CheckoutSuccessPage.ets(41:7)", "entry");
            // 操作按钮
            Column.width('100%');
            // 操作按钮
            Column.padding({ bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('继续购物');
            Button.debugLine("entry/src/main/ets/pages/CheckoutSuccessPage.ets(42:9)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.backgroundColor('#ff6b35');
            Button.margin({ bottom: 16 });
            Button.onClick(() => {
                router.clear();
                router.pushUrl({
                    url: 'view/BottomTabs'
                });
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('查看订单');
            Button.debugLine("entry/src/main/ets/pages/CheckoutSuccessPage.ets(54:9)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.backgroundColor('#fff');
            Button.fontColor('#ff6b35');
            Button.border({ width: 1, color: '#ff6b35' });
            Button.onClick(() => {
                // TODO: 跳转到订单页面
                console.log('查看订单功能待实现');
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "CheckoutSuccessPage";
    }
}
registerNamedRoute(() => new CheckoutSuccessPage(undefined, {}), "", { bundleName: "com.example.shi_yan_3", moduleName: "entry", pagePath: "pages/CheckoutSuccessPage", pageFullPath: "entry/src/main/ets/pages/CheckoutSuccessPage", integratedHsp: "false", moduleType: "followWithHap" });
