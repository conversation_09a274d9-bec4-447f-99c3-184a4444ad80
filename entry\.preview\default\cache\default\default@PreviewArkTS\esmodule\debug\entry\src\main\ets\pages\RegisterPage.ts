if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface RegisterPage_Params {
    username?: string;
    password?: string;
    confirmPassword?: string;
    viewModel?: UserViewModel;
    context?;
}
import promptAction from "@ohos:promptAction";
import router from "@ohos:router";
import { UserViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/UserViewModel&";
import type { User } from '../model/User';
import type common from "@ohos:app.ability.common";
class RegisterPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__username = new ObservedPropertySimplePU('', this, "username");
        this.__password = new ObservedPropertySimplePU('', this, "password");
        this.__confirmPassword = new ObservedPropertySimplePU('', this, "confirmPassword");
        this.viewModel = new UserViewModel();
        this.context = getContext(this) as common.UIAbilityContext;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: RegisterPage_Params) {
        if (params.username !== undefined) {
            this.username = params.username;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
        if (params.confirmPassword !== undefined) {
            this.confirmPassword = params.confirmPassword;
        }
        if (params.viewModel !== undefined) {
            this.viewModel = params.viewModel;
        }
        if (params.context !== undefined) {
            this.context = params.context;
        }
    }
    updateStateVars(params: RegisterPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__username.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
        this.__confirmPassword.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__username.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        this.__confirmPassword.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __username: ObservedPropertySimplePU<string>;
    get username() {
        return this.__username.get();
    }
    set username(newValue: string) {
        this.__username.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>;
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    private __confirmPassword: ObservedPropertySimplePU<string>;
    get confirmPassword() {
        return this.__confirmPassword.get();
    }
    set confirmPassword(newValue: string) {
        this.__confirmPassword.set(newValue);
    }
    private viewModel: UserViewModel;
    private context;
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 20 });
            Column.debugLine("entry/src/main/ets/pages/RegisterPage.ets(17:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.justifyContent(FlexAlign.Start);
            Column.alignItems(HorizontalAlign.Center);
            Column.backgroundColor('#F1F3F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("用户注册");
            Text.debugLine("entry/src/main/ets/pages/RegisterPage.ets(18:7)", "entry");
            Text.fontSize(40);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ top: 80, bottom: 40 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入用户名' });
            TextInput.debugLine("entry/src/main/ets/pages/RegisterPage.ets(23:7)", "entry");
            TextInput.width('80%');
            TextInput.onChange((value) => {
                this.username = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请设置密码' });
            TextInput.debugLine("entry/src/main/ets/pages/RegisterPage.ets(29:7)", "entry");
            TextInput.type(InputType.Password);
            TextInput.width('80%');
            TextInput.onChange((value) => {
                this.password = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请再次输入密码' });
            TextInput.debugLine("entry/src/main/ets/pages/RegisterPage.ets(36:7)", "entry");
            TextInput.type(InputType.Password);
            TextInput.width('80%');
            TextInput.onChange((value) => {
                this.confirmPassword = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('注册');
            Button.debugLine("entry/src/main/ets/pages/RegisterPage.ets(43:7)", "entry");
            Button.width('80%');
            Button.margin({ top: 20 });
            Button.onClick(async () => {
                if (this.password !== this.confirmPassword) {
                    promptAction.showToast({ message: '两次输入的密码不一致' });
                    return;
                }
                const user: User = { username: this.username, password: this.password };
                await this.viewModel.init(this.context);
                const result = await this.viewModel.register(user);
                if (result.success) {
                    promptAction.showToast({ message: result.message });
                    router.replaceUrl({ url: 'pages/LoginPage' });
                }
                else {
                    promptAction.showToast({ message: result.message });
                }
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 10 });
            Row.debugLine("entry/src/main/ets/pages/RegisterPage.ets(62:7)", "entry");
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("已有账号？");
            Text.debugLine("entry/src/main/ets/pages/RegisterPage.ets(63:9)", "entry");
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create("去登录");
            Text.debugLine("entry/src/main/ets/pages/RegisterPage.ets(64:9)", "entry");
            Text.fontColor(Color.Blue);
            Text.onClick(() => {
                router.replaceUrl({ url: 'pages/LoginPage' });
            });
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "RegisterPage";
    }
}
registerNamedRoute(() => new RegisterPage(undefined, {}), "", { bundleName: "com.example.shi_yan_3", moduleName: "entry", pagePath: "pages/RegisterPage", pageFullPath: "entry/src/main/ets/pages/RegisterPage", integratedHsp: "false", moduleType: "followWithHap" });
